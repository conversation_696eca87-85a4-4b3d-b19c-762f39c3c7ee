-- ~/.config/lvim/config.lua
-- 主配置文件，用于加载所有模块化配置

-- 设置 leader 键
lvim.leader = ","

-- 加载自定义模块
-- 注意：顺序很重要，通常先加载选项，再加载插件和快捷键
require("user.options")
require("user.plugins")
require("user.keymaps")

-- 您原有的 which-key 配置，现在更清晰地放在主文件中
lvim.builtin.which_key.mappings["C"] = {
  name = "Copilot", -- Copilot 子菜单
  p = { "<cmd>Copilot panel<CR>", "打开 Copilot 面板" },
  e = { "<cmd>Copilot enable<CR>", "启用 Copilot" },
  d = { "<cmd>Copilot disable<CR>", "禁用 Copilot" },
  s = { "<cmd>Copilot status<CR>", "Copilot 状态" },
  c = { "<cmd>CopilotChatToggle<CR>", "切换 Copilot Chat" },
  q = { "<cmd>CopilotChat<CR>", "提问 Copilot" },
  f = { "<cmd>CopilotChatFix<CR>", "修复代码" },
  t = { "<cmd>CopilotChatTests<CR>", "生成测试" },
  a = { "<cmd>CopilotChatExplain<CR>", "解释代码" },
  v = { "<cmd>CopilotChatReview<CR>", "代码审查" },
  m = { "<cmd>CopilotChatOptimize<CR>", "优化代码" },
  h = { "<cmd>CopilotChatCommit<CR>", "生成提交信息" },
  z = {
    name = "中文提示",
    e = { "<cmd>CopilotChatChineseExplain<CR>", "中文解释代码" },
    r = { "<cmd>CopilotChatChineseReview<CR>", "中文代码审查" },
    f = { "<cmd>CopilotChatChineseRefactor<CR>", "中文重构建议" },
  },
  g = {
    name = "Agent 功能",
    a = { "<cmd>CopilotChatAgents<CR>", "查看可用 Agents" },
    w = { "<cmd>CopilotChat @copilot-workspace<CR>", "工作区 Agent" },
  },
  j = {
    name = "Agent 提示",
    s = { "<cmd>CopilotChatSearchProject<CR>", "搜索项目代码" },
    t = { "<cmd>CopilotChatRunTests<CR>", "运行测试" },
  },
  y = {
    name = "中文 Agent 提示",
    s = { "<cmd>CopilotChatChineseSearchProject<CR>", "中文搜索项目代码" },
    t = { "<cmd>CopilotChatChineseRunTests<CR>", "中文运行测试" },
  },
  a = {
    name = "Augment AI",
    s = { "<cmd>Augment status<CR>", "查看状态" },
    i = { "<cmd>Augment signin<CR>", "登录" },
    o = { "<cmd>Augment signout<CR>", "登出" },
    c = { "<cmd>Augment chat<CR>", "发送聊天消息" },
  }
}
